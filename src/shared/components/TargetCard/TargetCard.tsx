import { Badge, Card, Container } from '@ghq-abi/design-system-v2';

import { getTargetCardFooterText } from '~/shared/utils/getTargetCardFooterText';

import { TargetFooter } from './components/TargetFooter';
import { ChildCard } from './ChildCard';
import { ParentCard } from './ParentCard';
import { TargetCardProps } from './types';

export function TargetCard({
  data,
  proposalStatus,
  currentTargetType,
  hideChildren,
  onEditTarget,
  onOpenComments,
  onRemoveActionClick,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
  isOnDroppableArea,
  isOnCatalogWithTabs = false,
  onAcceptTarget,
  isTargetAccepted,
  isTargetLoading,
  showBadgesInProposalTab = false,
}: TargetCardProps) {
  const footerText = getTargetCardFooterText(
    proposalStatus,
    currentTargetType,
    data,
  );

  const filteredChildren = currentTargetType
    ? data.children?.filter(child =>
        child.targetTypes?.some(
          targetType => targetType.type === currentTargetType,
        ),
      )
    : data.children;

  const renderFooterBadge = () => {
    if (!footerText) {
      return null;
    }

    const isAgree = footerText === 'I Agree';
    const isDisagree = footerText === "Don't agree";

    if (isAgree) {
      return (
        <Badge className="border border-green-500 text-green-500 bg-transparent">
          {footerText}
        </Badge>
      );
    }

    if (isDisagree) {
      return (
        <Badge className="border border-red-500 text-red-500 bg-transparent">
          {footerText}
        </Badge>
      );
    }

    return null;
  };

  const renderFooterContent = () => {
    if (isOnCatalogWithTabs) {
      return (
        <TargetFooter
          target={data}
          isOnCatalogWithTabs={isOnCatalogWithTabs}
          showBadgesInProposalTab={showBadgesInProposalTab}
          onAcceptTarget={onAcceptTarget}
          isTargetAccepted={isTargetAccepted}
          isTargetLoading={isTargetLoading}
        />
      );
    }

    return (
      <Container className="flex justify-end">{renderFooterBadge()}</Container>
    );
  };

  return (
    <Card.Root round="md">
      <ParentCard
        data={data}
        onRemoveActionClick={onRemoveActionClick}
        isDrawer={isDrawer}
        isOnCatalogWithTabs={isOnCatalogWithTabs}
      />
      {!hideChildren && (
        <Card.Content className="flex flex-col gap-4">
          {filteredChildren?.map(child => (
            <ChildCard
              key={child.uid}
              target={child}
              disableDrag={true}
              onEditTarget={onEditTarget}
              onOpenComments={onOpenComments}
              onRemoveActionClick={onRemoveActionClick}
              hasManagerPermission={hasManagerPermission}
              hasEmployeePermission={hasEmployeePermission}
              isDrawer={isDrawer}
              showActions={isOnDroppableArea}
              isOnCatalogWithTabs={isOnCatalogWithTabs}
              isInsideTargetCard={true}
            />
          ))}
          {renderFooterContent()}
        </Card.Content>
      )}
    </Card.Root>
  );
}
